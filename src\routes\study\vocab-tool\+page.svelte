<script lang="ts">
	import Button from '$lib/ui/Button.svelte';
	import H1 from '$lib/ui/typography/H1.svelte';
	import P1 from '$lib/ui/typography/P1.svelte';
	import P2 from '$lib/ui/typography/P2.svelte';
	import PopUp from '$lib/ui/PopUp.svelte';
	import Input from '$lib/ui/Input.svelte';
	import LoadingButton from '$lib/ui/LoadingButton.svelte';
	import type { Deck } from '$lib/types';
	import { browser } from '$app/environment';
	import { type Unsubscribe } from 'firebase/firestore';
	import { addCardToDeck, calculateDeckStats, createDeck, subscribeToUserDecks } from '$lib/firebase';
	import { onDestroy } from 'svelte';
	import HelpIcon from '$lib/ui/HelpIcon.svelte';
	
	let { data } = $props();

	// State for managing the pop-up
	let popUpState = $state<"Not Open" | "Add Deck" | "Add Cards">("Not Open");
	let isPopupOpen = $derived(popUpState !== "Not Open");
	let isAddCardsOpen = $derived(popUpState === "Add Cards");
	let isAddDeckOpen = $derived(popUpState === "Add Deck");

	// State for managing the deck creation form
	let deckName = $state('');
	let deckDescription = $state('');
	$effect(() => {
		if (!isAddDeckOpen) {
			deckDescription = '';
			deckName = '';
		}
	})

	let cardName: string = $state('');
	$effect(() => {
		if (!isAddCardsOpen) {
			cardName = "";
		}
	})

	// State for managing decks and Firebase subscription
	let decks = $state<Deck[] | null>(null);
	let currentDeckIndex = $state<number | null>(null);
	let currentDeckData = $derived<Deck | null>(currentDeckIndex !== null ? decks[currentDeckIndex] : null)

	let unsubscriber = $state<Unsubscribe | null>(null);
	
	// Initialize deck subscriptions when component mounts
	if (data.uid && browser) {
		unsubscriber = subscribeToUserDecks(data.uid, (d) => {
			decks = d;
		});
	}

	// Clean up the listener when the component is destroyed
	onDestroy(() => {
		if (unsubscriber) {
			unsubscriber();
		}
	});

	async function handleDelete(deck: Deck) {
		// TODO: Implement deck deletion
		console.log('Delete deck:', deck.id);
	}

	function openAddDeck() {
		popUpState = "Add Deck";
	}

	function closePopUp() {
		popUpState = "Not Open";
	}

	function closeDeck() {
		currentDeckIndex = null;
	}

	function openAddCards() {
		popUpState = "Add Cards";
	}

	// Add this function to handle the add deck form submission
	async function handleAddDeckFormSubmit(e: Event) {
		// Prevent default form submission behavior
		e.preventDefault();
		
		// Check if the deck name is empty
		if (!deckName.trim()) {
			alert('Please enter a deck name');
			return;
		}

		if (!deckDescription.trim()) {
			alert("Please enter a deck description");
			return;
		}

		// Create the deck
		await createDeck(data.uid, deckName.trim(), deckDescription.trim());
		closePopUp();
	}

	function handleFetchDeck(deckIndex: number) {
		currentDeckIndex = deckIndex; 
	}

	let isAddingCard = $state(false);
	let message = $state('');

	// Adds a new vocabulary card to the current deck
	async function addCard(e: Event, word: string) {
		e.preventDefault();

		// Helper function to display a message and reset the adding state
		function displayMessage(msg: string) {
			message = msg;
			isAddingCard = false;
		}
		
		// Set loading state to true while processing the card'
		message = '';
		isAddingCard = true;
		
		// Validate input - check if word is empty or only whitespace
		if (!word.trim()) {
			displayMessage('Please enter a word');
			return;
		}

		// Make API call to generate vocabulary data for the word
		const response = await fetch('/api/vocab-tool/generate-vocab-data', {
			method: 'POST',
			body: JSON.stringify({ word, uid: data.uid }),
		});

		// Parse the response from the API
		const { message: msg, success, vocabId } = await response.json();
		
		// Show the user error message if unsucessful
		if (!success) {
			displayMessage(msg);
			return;
		}

		// Add the generated card to the current deck
		await addCardToDeck(data.uid, currentDeckData.id, vocabId);
		
		// Show success message to the user
		displayMessage('Card added successfully! You can add another card or start studying.');
	}
</script>

<svelte:head>
	<title>DSAT16 - Vocab Tool</title>
</svelte:head>

<div class="decks-bg">
	<header class="decks-header">
		<H1>VOCAB16</H1>
	</header>
	<main class="decks-main">
		<section class="decks-card">
			{#if currentDeckData}
					{@const stats = calculateDeckStats(currentDeckData.cards)}
					<div class="deck-header">
						<button onclick={() => closeDeck()} aria-label="Close deck">
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M8.29 11.29C8.19896 11.3851 8.12759 11.4972 8.08 11.62C7.97998 11.8635 7.97998 12.1365 8.08 12.38C8.12759 12.5028 8.19896 12.6149 8.29 12.71L11.29 15.71C11.4783 15.8983 11.7337 16.0041 12 16.0041C12.2663 16.0041 12.5217 15.8983 12.71 15.71C12.8983 15.5217 13.0041 15.2663 13.0041 15C13.0041 14.7337 12.8983 14.4783 12.71 14.29L11.41 13H15C15.2652 13 15.5196 12.8946 15.7071 12.7071C15.8946 12.5196 16 12.2652 16 12C16 11.7348 15.8946 11.4804 15.7071 11.2929C15.5196 11.1054 15.2652 11 15 11H11.41L12.71 9.71C12.8037 9.61704 12.8781 9.50644 12.9289 9.38458C12.9797 9.26272 13.0058 9.13201 13.0058 9C13.0058 8.86799 12.9797 8.73728 12.9289 8.61542C12.8781 8.49356 12.8037 8.38296 12.71 8.29C12.617 8.19627 12.5064 8.12188 12.3846 8.07111C12.2627 8.02034 12.132 7.9942 12 7.9942C11.868 7.9942 11.7373 8.02034 11.6154 8.07111C11.4936 8.12188 11.383 8.19627 11.29 8.29L8.29 11.29ZM2 12C2 13.9778 2.58649 15.9112 3.6853 17.5557C4.78412 19.2002 6.3459 20.4819 8.17317 21.2388C10.0004 21.9957 12.0111 22.1937 13.9509 21.8079C15.8907 21.422 17.6725 20.4696 19.0711 19.0711C20.4696 17.6725 21.422 15.8907 21.8079 13.9509C22.1937 12.0111 21.9957 10.0004 21.2388 8.17317C20.4819 6.3459 19.2002 4.78412 17.5557 3.6853C15.9112 2.58649 13.9778 2 12 2C10.6868 2 9.38642 2.25866 8.17317 2.7612C6.95991 3.26375 5.85752 4.00035 4.92893 4.92893C3.05357 6.8043 2 9.34784 2 12ZM20 12C20 13.5823 19.5308 15.129 18.6518 16.4446C17.7727 17.7602 16.5233 18.7855 15.0615 19.391C13.5997 19.9965 11.9911 20.155 10.4393 19.8463C8.88743 19.5376 7.46197 18.7757 6.34315 17.6569C5.22433 16.538 4.4624 15.1126 4.15372 13.5607C3.84504 12.0089 4.00346 10.4003 4.60896 8.93853C5.21447 7.47672 6.23984 6.22729 7.55544 5.34824C8.87103 4.46919 10.4177 4 12 4C14.1217 4 16.1566 4.84285 17.6569 6.34315C19.1571 7.84344 20 9.87827 20 12Z" fill="black"/>
							</svg>			
						</button>
						<P1 isBold={true}>{currentDeckData.name}</P1>
						<!-- <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M21.32 9.55L19.43 8.92L20.32 7.14C20.4102 6.95369 20.4404 6.74397 20.4064 6.53978C20.3723 6.33558 20.2758 6.14699 20.13 6L18 3.87C17.8522 3.72209 17.6618 3.62421 17.4555 3.59013C17.2493 3.55605 17.0375 3.58748 16.85 3.68L15.07 4.57L14.44 2.68C14.3735 2.483 14.2472 2.31163 14.0787 2.18975C13.9102 2.06787 13.7079 2.00155 13.5 2H10.5C10.2904 1.99946 10.0858 2.06482 9.91537 2.18685C9.7449 2.30887 9.61709 2.48138 9.55 2.68L8.92 4.57L7.14 3.68C6.95369 3.58978 6.74397 3.55961 6.53978 3.59364C6.33558 3.62767 6.14699 3.72423 6 3.87L3.87 6C3.72209 6.14777 3.62421 6.33818 3.59013 6.54446C3.55605 6.75074 3.58748 6.96251 3.68 7.15L4.57 8.93L2.68 9.56C2.483 9.62654 2.31163 9.75283 2.18975 9.92131C2.06787 10.0898 2.00155 10.2921 2 10.5V13.5C1.99946 13.7096 2.06482 13.9142 2.18685 14.0846C2.30887 14.2551 2.48138 14.3829 2.68 14.45L4.57 15.08L3.68 16.86C3.58978 17.0463 3.55961 17.256 3.59364 17.4602C3.62767 17.6644 3.72423 17.853 3.87 18L6 20.13C6.14777 20.2779 6.33818 20.3758 6.54446 20.4099C6.75074 20.444 6.96251 20.4125 7.15 20.32L8.93 19.43L9.56 21.32C9.62709 21.5186 9.7549 21.6911 9.92537 21.8132C10.0958 21.9352 10.3004 22.0005 10.51 22H13.51C13.7196 22.0005 13.9242 21.9352 14.0946 21.8132C14.2651 21.6911 14.3929 21.5186 14.46 21.32L15.09 19.43L16.87 20.32C17.0551 20.4079 17.2628 20.4369 17.4649 20.4029C17.667 20.3689 17.8538 20.2737 18 20.13L20.13 18C20.2779 17.8522 20.3758 17.6618 20.4099 17.4555C20.444 17.2493 20.4125 17.0375 20.32 16.85L19.43 15.07L21.32 14.44C21.517 14.3735 21.6884 14.2472 21.8103 14.0787C21.9321 13.9102 21.9985 13.7079 22 13.5V10.5C22.0005 10.2904 21.9352 10.0858 21.8132 9.91537C21.6911 9.7449 21.5186 9.61709 21.32 9.55ZM20 12.78L18.8 13.18C18.5241 13.2695 18.2709 13.418 18.0581 13.6151C17.8452 13.8122 17.6778 14.0533 17.5675 14.3216C17.4571 14.5899 17.4064 14.879 17.419 15.1688C17.4315 15.4586 17.5069 15.7422 17.64 16L18.21 17.14L17.11 18.24L16 17.64C15.7436 17.5122 15.4627 17.4411 15.1763 17.4313C14.89 17.4215 14.6049 17.4734 14.3403 17.5834C14.0758 17.6934 13.8379 17.8589 13.6429 18.0688C13.4479 18.2787 13.3003 18.5281 13.21 18.8L12.81 20H11.22L10.82 18.8C10.7305 18.5241 10.582 18.2709 10.3849 18.0581C10.1878 17.8452 9.94671 17.6778 9.67842 17.5675C9.41014 17.4571 9.12105 17.4064 8.83123 17.419C8.5414 17.4315 8.25777 17.5069 8 17.64L6.86 18.21L5.76 17.11L6.36 16C6.4931 15.7422 6.56852 15.4586 6.58105 15.1688C6.59358 14.879 6.5429 14.5899 6.43254 14.3216C6.32218 14.0533 6.15478 13.8122 5.94195 13.6151C5.72912 13.418 5.47595 13.2695 5.2 13.18L4 12.78V11.22L5.2 10.82C5.47595 10.7305 5.72912 10.582 5.94195 10.3849C6.15478 10.1878 6.32218 9.94671 6.43254 9.67842C6.5429 9.41014 6.59358 9.12105 6.58105 8.83123C6.56852 8.5414 6.4931 8.25777 6.36 8L5.79 6.89L6.89 5.79L8 6.36C8.25777 6.4931 8.5414 6.56852 8.83123 6.58105C9.12105 6.59358 9.41014 6.5429 9.67842 6.43254C9.94671 6.32218 10.1878 6.15478 10.3849 5.94195C10.582 5.72912 10.7305 5.47595 10.82 5.2L11.22 4H12.78L13.18 5.2C13.2695 5.47595 13.418 5.72912 13.6151 5.94195C13.8122 6.15478 14.0533 6.32218 14.3216 6.43254C14.5899 6.5429 14.879 6.59358 15.1688 6.58105C15.4586 6.56852 15.7422 6.4931 16 6.36L17.14 5.79L18.24 6.89L17.64 8C17.5122 8.25645 17.4411 8.53735 17.4313 8.82369C17.4215 9.11003 17.4734 9.39513 17.5834 9.65969C17.6934 9.92424 17.8589 10.1621 18.0688 10.3571C18.2787 10.5521 18.5281 10.6997 18.8 10.79L20 11.19V12.78ZM12 8C11.2089 8 10.4355 8.2346 9.77772 8.67413C9.11993 9.11365 8.60724 9.73836 8.30448 10.4693C8.00173 11.2002 7.92252 12.0044 8.07686 12.7804C8.2312 13.5563 8.61217 14.269 9.17158 14.8284C9.73099 15.3878 10.4437 15.7688 11.2196 15.9231C11.9956 16.0775 12.7998 15.9983 13.5307 15.6955C14.2616 15.3928 14.8864 14.8801 15.3259 14.2223C15.7654 13.5645 16 12.7911 16 12C16 10.9391 15.5786 9.92172 14.8284 9.17158C14.0783 8.42143 13.0609 8 12 8ZM12 14C11.6044 14 11.2178 13.8827 10.8889 13.6629C10.56 13.4432 10.3036 13.1308 10.1522 12.7654C10.0009 12.3999 9.96126 11.9978 10.0384 11.6098C10.1156 11.2219 10.3061 10.8655 10.5858 10.5858C10.8655 10.3061 11.2219 10.1156 11.6098 10.0384C11.9978 9.96126 12.3999 10.0009 12.7654 10.1522C13.1308 10.3036 13.4432 10.56 13.6629 10.8889C13.8827 11.2178 14 11.6044 14 12C14 12.5304 13.7893 13.0391 13.4142 13.4142C13.0391 13.7893 12.5304 14 12 14Z" fill="black"/>
						</svg> -->
						<div style="width: 24px"></div>
					</div>
					<div class="deck-stats-description">
						<P2>{currentDeckData.description || 'No description provided.'}</P2>
					</div>
					<div class="deck-stats-list">
						<div class="deck-stat-row">
							<P2 isBold={true}>Total Cards:</P2>
							<P2>{stats.total}</P2>
						</div>
						<div class="deck-stat-row" style="--text-color: var(--aquamarine)">
							<P2 isBold={true}>
								<HelpIcon --bg-color=var(--text-color) title="The total number of words in the deck">New</HelpIcon>
							</P2>
							<P2>{stats.new}</P2>
						</div>
						<div class="deck-stat-row" style="--text-color: var(--sky-blue)">
							<P2 isBold={true}>
								<HelpIcon --bg-color=var(--text-color) title="Words you are currently learning">(Re)Learning</HelpIcon>
							</P2>
							<P2>{stats.learn}</P2>
						</div>
						<div class="deck-stat-row" style="--text-color: var(--tangerine)">
							<P2 isBold={true} >
								<HelpIcon --bg-color=var(--text-color) title="Words you've learned">Reviewing</HelpIcon>
							</P2>
							<P2>{stats.review}</P2>
						</div>
						<div class="deck-stat-row" style="--text-color: var(--rose)">
							<P2 isBold={true}>
								<HelpIcon --bg-color=var(--text-color) title="Words scheduled for review today">Due</HelpIcon>
							</P2>
							<P2>{stats.due}</P2>
						</div>
					</div>
					<div class="deck-stats-actions">
						<a href="/study/vocab-tool/{currentDeckData.id}"><Button>Start</Button></a>
						<Button --button-bg-color=var(--aquamarine) onclick={openAddCards}>Add Cards</Button>
					</div>
			{:else}
				<div class="decks-list-header">
					<div class="deck-name-col"><P1 isBold={true}>Deck</P1></div>
					<div class="stat-col"><P1 isBold={true} --text-color="var(--rose)"><HelpIcon title="Words to review soon">Due</HelpIcon></P1></div>
					<div class="stat-col"><P1 isBold={true}><HelpIcon title="The total number of words in the deck">Total</HelpIcon></P1></div>
					<div class="remove-col"></div>
				</div>
				<div class="decks-list-body">
				{#if decks === null }
					<div class="loading-container">
						<div class="loading-spinner"></div>
						<P2>Loading your decks...</P2>
					</div>
				{:else if decks.length === 0}
					<div class="empty-state">
						<P2>No decks yet. Create your first deck to get started!</P2>
					</div>
				{:else}
					{#each decks as deck, deckIndex}
						{@const stats = calculateDeckStats(deck.cards)}
						<button class="decks-list-row" tabindex="0" onclick={() => handleFetchDeck(deckIndex)}>
							<div class="deck-name-col" ><P2 isBold={true}>{deck.name}</P2></div>
							<div class="stat-col"><P2 --text-color="var(--rose)">{stats.due}</P2></div>
							<div class="stat-col"><P2>{stats.total}</P2></div>
							<div 
								class="delete-btn" 
								role="button" 
								aria-label="Delete deck" 
								tabindex="0" 
								onkeydown={(e) => e.key === 'Enter' && handleDelete(deck)} 
								onclick={(e) => { e.stopPropagation(); handleDelete(deck); 
							}}>
								<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M14.714 6.86634L13.454 6.44634L14.0473 5.25968C14.1075 5.13547 14.1276 4.99565 14.1049 4.85952C14.0822 4.7234 14.0178 4.59766 13.9207 4.49968L12.5007 3.07968C12.4021 2.98107 12.2752 2.91581 12.1377 2.89309C12.0002 2.87037 11.859 2.89133 11.734 2.95301L10.5473 3.54634L10.1273 2.28634C10.083 2.155 9.99877 2.04076 9.88645 1.95951C9.77413 1.87826 9.63928 1.83404 9.50065 1.83301H7.50065C7.36089 1.83265 7.22455 1.87622 7.1109 1.95757C6.99725 2.03892 6.91204 2.15393 6.86732 2.28634L6.44732 3.54634L5.26065 2.95301C5.13644 2.89286 4.99663 2.87275 4.8605 2.89543C4.72437 2.91812 4.59864 2.9825 4.50065 3.07968L3.08065 4.49968C2.98204 4.59819 2.91679 4.72513 2.89407 4.86265C2.87135 5.00017 2.8923 5.14135 2.95399 5.26634L3.54732 6.45301L2.28732 6.87301C2.15598 6.91737 2.04174 7.00156 1.96049 7.11388C1.87923 7.2262 1.83502 7.36105 1.83399 7.49968V9.49968C1.83363 9.63944 1.8772 9.77578 1.95855 9.88943C2.0399 10.0031 2.15491 10.0883 2.28732 10.133L3.54732 10.553L2.95399 11.7397C2.89384 11.8639 2.87372 12.0037 2.89641 12.1398C2.9191 12.276 2.98347 12.4017 3.08065 12.4997L4.50065 13.9197C4.59916 14.0183 4.7261 14.0835 4.86362 14.1063C5.00114 14.129 5.14233 14.108 5.26732 14.0463L6.45399 13.453L6.87399 14.713C6.91871 14.8454 7.00392 14.9604 7.11757 15.0418C7.23122 15.1231 7.36756 15.1667 7.50732 15.1663H9.50732C9.64708 15.1667 9.78342 15.1231 9.89707 15.0418C10.0107 14.9604 10.0959 14.8454 10.1407 14.713L10.5607 13.453L11.7473 14.0463C11.8707 14.105 12.0092 14.1243 12.1439 14.1016C12.2787 14.079 12.4032 14.0154 12.5007 13.9197L13.9207 12.4997C14.0193 12.4012 14.0845 12.2742 14.1072 12.1367C14.13 11.9992 14.109 11.858 14.0473 11.733L13.454 10.5463L14.714 10.1263C14.8453 10.082 14.9596 9.99779 15.0408 9.88547C15.1221 9.77315 15.1663 9.6383 15.1673 9.49968V7.49968C15.1677 7.35991 15.1241 7.22357 15.0428 7.10992C14.9614 6.99627 14.8464 6.91107 14.714 6.86634ZM13.834 9.01968L13.034 9.28634C12.85 9.34601 12.6812 9.44499 12.5394 9.57641C12.3975 9.70783 12.2859 9.86854 12.2123 10.0474C12.1387 10.2263 12.1049 10.419 12.1133 10.6122C12.1216 10.8054 12.1719 10.9945 12.2607 11.1663L12.6407 11.9263L11.9073 12.6597L11.1673 12.2597C10.9964 12.1745 10.8091 12.1271 10.6182 12.1205C10.4273 12.114 10.2372 12.1486 10.0609 12.2219C9.8845 12.2953 9.72594 12.4056 9.59593 12.5456C9.46592 12.6855 9.3675 12.8517 9.30732 13.033L9.04065 13.833H7.98065L7.71399 13.033C7.65431 12.849 7.55534 12.6803 7.42392 12.5384C7.2925 12.3965 7.13179 12.2849 6.95293 12.2113C6.77408 12.1377 6.58135 12.104 6.38814 12.1123C6.19492 12.1207 6.00583 12.1709 5.83399 12.2597L5.07399 12.6397L4.34065 11.9063L4.74065 11.1663C4.82938 10.9945 4.87967 10.8054 4.88802 10.6122C4.89637 10.419 4.86258 10.2263 4.78901 10.0474C4.71544 9.86854 4.60384 9.70783 4.46195 9.57641C4.32006 9.44499 4.15128 9.34601 3.96732 9.28634L3.16732 9.01968V7.97968L3.96732 7.71301C4.15128 7.65334 4.32006 7.55436 4.46195 7.42294C4.60384 7.29152 4.71544 7.13081 4.78901 6.95196C4.86258 6.7731 4.89637 6.58038 4.88802 6.38716C4.87967 6.19394 4.82938 6.00485 4.74065 5.83301L4.36065 5.09301L5.09399 4.35968L5.83399 4.73968C6.00583 4.82841 6.19492 4.87869 6.38814 4.88704C6.58135 4.89539 6.77408 4.86161 6.95293 4.78804C7.13179 4.71446 7.2925 4.60286 7.42392 4.46097C7.55534 4.31908 7.65431 4.15031 7.71399 3.96634L7.98065 3.16634H9.02065L9.28732 3.96634C9.34699 4.15031 9.44597 4.31908 9.57739 4.46097C9.7088 4.60286 9.86951 4.71446 10.0484 4.78804C10.2272 4.86161 10.42 4.89539 10.6132 4.88704C10.8064 4.87869 10.9955 4.82841 11.1673 4.73968L11.9273 4.35968L12.6607 5.09301L12.2607 5.83301C12.1755 6.00397 12.128 6.19124 12.1215 6.38214C12.115 6.57303 12.1496 6.7631 12.2229 6.93946C12.2962 7.11583 12.4066 7.27439 12.5465 7.4044C12.6865 7.5344 12.8527 7.63283 13.034 7.69301L13.834 7.95968V9.01968ZM8.50065 5.83301C7.97324 5.83301 7.45766 5.98941 7.01913 6.28242C6.5806 6.57544 6.23881 6.99192 6.03697 7.47919C5.83514 7.96646 5.78233 8.50263 5.88523 9.01992C5.98812 9.5372 6.24209 10.0124 6.61503 10.3853C6.98797 10.7582 7.46313 11.0122 7.98041 11.1151C8.49769 11.218 9.03387 11.1652 9.52114 10.9634C10.0084 10.7615 10.4249 10.4197 10.7179 9.9812C11.0109 9.54267 11.1673 9.02709 11.1673 8.49968C11.1673 7.79243 10.8864 7.11416 10.3863 6.61406C9.88617 6.11396 9.2079 5.83301 8.50065 5.83301V5.83301ZM8.50065 9.83301C8.23694 9.83301 7.97916 9.75481 7.75989 9.6083C7.54063 9.46179 7.36973 9.25356 7.26881 9.00992C7.1679 8.76629 7.14149 8.4982 7.19294 8.23956C7.24439 7.98091 7.37137 7.74334 7.55784 7.55687C7.74431 7.3704 7.98189 7.24341 8.24053 7.19196C8.49917 7.14052 8.76726 7.16692 9.0109 7.26784C9.25453 7.36875 9.46277 7.53965 9.60928 7.75892C9.75579 7.97818 9.83399 8.23597 9.83399 8.49968C9.83399 8.8533 9.69351 9.19244 9.44346 9.44248C9.19341 9.69253 8.85427 9.83301 8.50065 9.83301Z" fill="black"/>
								</svg>  
							</div>
						</button>
					{/each}
				{/if}
					<div class="decks-actions">
						<Button --button-bg-color="var(--sky-blue)" onclick={openAddDeck}>Create Deck</Button>
						<!-- <P1>Or</P1>
						<Button --button-bg-color="var(--aquamarine)">Import</Button> -->
					</div>
				</div>
			{/if}
		</section>
	</main>
</div>

<PopUp --popup-color="var(--light-tangerine)" --close-button-color="var(--tangerine)" bind:isOpen={isPopupOpen} size="large" onclose={closePopUp}>
	{#if isAddDeckOpen}
	<form class="add-deck-popup-content" onsubmit={handleAddDeckFormSubmit}>
		<H1 --text-align=center>Create A New Deck</H1>
		<Input
			label="Deck name:"
			id="deck-name"
			placeholder="Enter deck name"
			required
			fullWidth
			bind:value={deckName}
		/>
		<Input
			label="Description:"
			id="deck-desc"
			placeholder="Enter description"
			fullWidth
			bind:value={deckDescription}
		/>
		<div class="add-deck-actions">
			<LoadingButton type="submit">Create</LoadingButton>
			<Button type="button" isSecondary onclick={closePopUp}>Cancel</Button>
		</div>
	</form>
	{:else if isAddCardsOpen}
	<div class="add-cards-popup-content">
		<H1 --text-align=center>Add Cards To Deck</H1>
		<div class="add-card-vocab-name">
			<Input label="Card name:" bind:value={cardName} id="card-name" placeholder="Enter card name" required fullWidth>
				<Button onclick={async (e) => await addCard(e, cardName)} disabled={isAddingCard}>
					{isAddingCard ? 'Adding...' : 'Add with AI'}
				</Button>
			</Input>
		</div>
		<P2>{@html message}</P2>
	</div>
	{/if}
</PopUp>

<style>
	.decks-bg {
		min-height: 100vh;
		background: var(--sky-blue);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
        gap: 2rem;
	}

	.deck-header {
		display: inline-flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		gap: 0.5rem;
        border-bottom: 1px solid var(--pitch-black);
		padding-bottom: 0.5rem;
	}

	.decks-header {
		display: flex;
		justify-content: center;
        padding: 0.75rem 3rem;
        background: var(--aquamarine);
        border: 0.5rem solid var(--pitch-black);
        justify-self: start;
	}

	.decks-main {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.decks-card {
		background: var(--white);
		border-radius: 1rem;
		border: 0.125rem solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		padding: 2rem;
		max-width: 50rem;
		width: calc(100% - 8rem);
	}

	.decks-list-header, .decks-list-row {
		display: flex;
		align-items: center;
		width: 100%;
	}
	.decks-list-header {
		font-weight: 700;
		border-bottom: 0.125rem solid var(--pitch-black);
		background: var(--white);
		padding: 0.75rem 0;
	}

	.decks-list-row {
		transition: background 0.2s;
		padding: 0.75rem 1rem;
	}
	.decks-list-row:hover {
		background: var(--light-sky-blue);
		text-decoration: underline;
		cursor: pointer;
	}

	.deck-name-col {
		flex: 1 1 0;
		text-align: left;
		padding-right: 1rem;
		min-width: 0;
		word-break: break-word;
	}

	.stat-col {
		flex: 1 0 0;
		text-align: center;
		--text-align: center;
		position: relative;
	}

    .remove-col {
        width: 3rem;
        text-align: center;
        position: relative;
    }

	.decks-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		margin-top: 1rem;
        align-items: center;
	}

    .delete-btn {
        visibility: hidden;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }

    .decks-list-row:hover .delete-btn {
        visibility: visible;
    }

	.add-deck-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		align-items: center;
		margin-top: 1rem;
	}

	@media (max-width: 768px) {
		.decks-card {
			padding: 1.25rem 0.25rem 1.25rem 0.25rem;
            width: calc(100% - 2rem);
		}

		.decks-list-header, .decks-list-row {
			padding: 0.5rem 0.5rem;
		}

        .delete-btn {
            width: 1rem;
            visibility: visible;
            margin: 0;
        }

        .remove-col {
            width: 1rem;
        }
	}

	.add-deck-popup-content,
	.add-cards-popup-content {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		min-width: 300px;
	}

	.add-card-vocab-name {
		display: inline-flex;
		gap: 1rem;
		align-items: center;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 1rem;
		padding: 2rem;
	}

	.loading-spinner {
		width: 2rem;
		height: 2rem;
		border: 0.25rem solid var(--light-sky-blue);
		border-top: 0.25rem solid var(--sky-blue);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 2rem;
		text-align: center;
		color: var(--charcoal);
	}

	.deck-stats-description {
		margin: 0.5rem 0 1.5rem 0;
		text-align: center;
	}

	.deck-stats-list {
		width: 100%;
		margin-bottom: 2rem;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.deck-stat-row {
		width: 100%;
		max-width: 18rem;
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.5rem;
	}

	.deck-stats-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
	}
</style>