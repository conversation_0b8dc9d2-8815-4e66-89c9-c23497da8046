<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A styled h5 element.
    
    Usage:
    ```tsx
    <H5>Heading 5</H5>
    ```
-->

<h5>
    {@render children?.()}
</h5>

<style>
    h5 {
        font-family: "Inter";
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-color, var(--pitch-black));
        text-align: var(--text-align, initial);
    }

    /* @media (max-width: 540px) {
        h1 {
            font-size: 2.25em;
        }
    } */
</style>
