<script>
    import { H2, H3, P1, P2, <PERSON><PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';
    
    let { 
        sectionLabel = "How It Works",
        headline = "Get setup in 3 easy steps",
        subheadline = "It takes a surprisingly small amount of effort to get addicted to learning.",
        steps = [
            {
                title: "Sign Up",
                description: "Create an account. It's free, and we never sell your data.",
                image: "STEP 1 IMAGE"
            },
            {
                title: "Do a Practice Test",
                description: "Take a practice test to see where you stand. We'll provide a personalized study plan.",
                image: "STEP 2 IMAGE"
            },
            {
                title: "{Step 3 Title}",
                description: "The third thing they need to do to get the oucome they want",
                image: "STEP 3 IMAGE"
            }
        ],
        primaryButton = "Get Started",
        secondaryButton = "Integrations"
    } = $props();
</script>

<SectionWrapper --bg-color="var(--light-sky-blue)" --padding-top="8rem" --padding-bottom="8rem">
<div class="how-it-works-section">
    <div class="how-it-works-header">
        <P2>{sectionLabel}</P2>
        <H2>{@html headline}</H2>
        <P1>{subheadline}</P1>
    </div>
    
    <div class="steps-grid">
        {#each steps as step}
            <div class="step-card">
                <div class="step-image">
                    <P2>{step.image}</P2>
                </div>
                <div class="step-content">
                    <H3>{@html step.title}</H3>
                    <P2>{step.description}</P2>
                </div>
            </div>
        {/each}
    </div>
    
    <div class="how-it-works-buttons">
        <SignUpButton>{primaryButton}</SignUpButton>
        <Button isSecondary>{secondaryButton}</Button>
    </div>
</div>
</SectionWrapper>

<style>
    /* How It Works Section */
    .how-it-works-section {
        max-width: 75rem;
        width: 100%;
        text-align: center;
    }
    
    .how-it-works-header {
        margin-bottom: 4rem;
    }
    
    .steps-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(21.875rem, 1fr));
        gap: 3rem;
        margin-bottom: 3rem;
    }
    
    .step-card {
        background: white;
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        overflow: hidden;
    }
    
    .step-card:hover {
        transform: translate(-0.25rem, -0.25rem);
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
    }
    
    .step-image {
        width: 100%;
        height: 12.5rem;
        background: var(--pitch-black);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
    }
    
    .step-content {
        padding: 2rem;
        text-align: left;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .how-it-works-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .steps-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
