<script>
    import { <PERSON><PERSON>, H2, H3, P1, P2 } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';
    
    let { 
        sectionLabel = "Core Features",
        headline = "Everything you need to get your 1600",
        subheadline = "Master the Digital SAT with our comprehensive platform designed by a perfect scorer.",
        features = [
            { color: "var(--rose)", icon: '<img src="/question-bank-icon.svg" alt="Question Bank" width="32" height="32" />', title: "Question Bank Practice", description: "Practice with thousands of high-quality questions organized by topic and difficulty, with detailed explanations for every answer." },
            { color: "var(--tangerine)", icon: '<img src="/simulation-icon.svg" alt="Simulations" width="32" height="32" />', title: "Simulations", description: "Take realistic Digital SAT simulations that adapt to your performance, just like the real test. Get instant scoring and detailed breakdowns." },
            { color: "var(--sky-blue)", icon: '<img src="/dashboard-icon.svg" alt="Dashboard" width="32" height="32" />', title: "Dashboard", description: "Track your progress, set goals, and stay motivated with our dashboard." },
            { color: "var(--aquamarine)", icon: '<img src="/vocab-tool-icon.svg" alt="AI Vocabulary Tool" width="32" height="32" />', title: "AI Vocabulary Tool", description: "Build your vocabulary with state-of-the-art spaced repetition algorithm that helps you learn more words in less time." },
        ],
        primaryButton = "See Them in Action",
        secondaryButton = "All Features"
    } = $props();
</script>

<SectionWrapper --bg-color="var(--purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="features-section">
    <div class="features-header">
        <P1>{sectionLabel}</P1>
        <H2>{@html headline}</H2>
        <P1>{subheadline}</P1>
    </div>
    
    <div class="features-grid">
        {#each features as feature}
            <div class="feature-card">
                <div class="feature-icon" style:background-color={feature.color}>{@html feature.icon}</div>
                <div class="feature-content">
                    <H3>{feature.title}</H3>
                    <P2>{feature.description}</P2>
                    <!-- <div class="feature-button">
                        <Button>Learn More</Button>
                    </div> -->
                </div>
            </div>
        {/each}
    </div>
    
    <div class="features-buttons">
        <SignUpButton>{primaryButton}</SignUpButton>
        <!-- <Button isSecondary>{secondaryButton}</Button> -->
    </div>
</div>
</SectionWrapper>

<style>
    /* Features Section */
    .features-section {
        max-width: 90rem;
        width: 100%;
        text-align: center;
        position: relative;
    }

    .features-header {
        margin: 0 auto 5rem auto;
        position: relative;
        padding: 2rem;
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        width: fit-content;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
        gap: 3rem;
        margin-bottom: 4rem;
    }

    .feature-card {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 0;
        text-align: left;
        display: flex;
        flex-direction: column;
        position: relative;
        box-shadow: 0.75rem 0.75rem 0 var(--pitch-black);
    }

    .feature-card:hover {
        transform: scale(1.02);
        box-shadow: 1rem 1rem 0 var(--pitch-black);
        z-index: 10;
    }

    .feature-icon {
        background: var(--white);
        width: 100%;
        height: 5rem;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0 2rem;
        border-bottom: 0.25rem solid var(--pitch-black);
        position: relative;
        border-radius: 0.75rem 0.75rem 0 0;
    }

    .feature-content {
        padding: 2rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        flex-grow: 1;
    }

    .features-buttons {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 3rem;
        position: relative;
    }

    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .features-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .features-header {
            margin-bottom: 3rem;
        }

        .feature-icon {
            height: 4rem;
        }

        .feature-content {
            padding: 1.5rem;
        }

        .features-buttons {
            gap: 1rem;
            margin-top: 2rem;
        }
    }
</style>
