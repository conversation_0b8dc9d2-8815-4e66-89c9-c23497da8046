<script>
    import { <PERSON><PERSON>, H2, H3, P1, P2 } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';

    let {
        sectionLabel = "Core Features",
        headline = "Everything you need to get your 1600",
        subheadline = "Master the Digital SAT with our comprehensive platform designed by a perfect scorer.",
        features = [
            {
                id: "question-bank",
                color: "var(--rose)",
                icon: '<img src="/question-bank-icon.svg" alt="Question Bank" width="32" height="32" />',
                title: "Question Bank Practice",
                description: "Practice with thousands of high-quality questions organized by topic and difficulty, with detailed explanations for every answer.",
                image: "/question-bank-demo.jpg"
            },
            {
                id: "simulations",
                color: "var(--tangerine)",
                icon: '<img src="/simulation-icon.svg" alt="Simulations" width="32" height="32" />',
                title: "Simulations",
                description: "Take realistic Digital SAT simulations that adapt to your performance, just like the real test. Get instant scoring and detailed breakdowns.",
                image: "/simulations-demo.jpg"
            },
            {
                id: "dashboard",
                color: "var(--sky-blue)",
                icon: '<img src="/dashboard-icon.svg" alt="Dashboard" width="32" height="32" />',
                title: "Dashboard",
                description: "Track your progress, set goals, and stay motivated with our dashboard.",
                image: "/dashboard-demo.jpg"
            },
            {
                id: "vocab-tool",
                color: "var(--aquamarine)",
                icon: '<img src="/vocab-tool-icon.svg" alt="AI Vocabulary Tool" width="32" height="32" />',
                title: "AI Vocabulary Tool",
                description: "Build your vocabulary with state-of-the-art spaced repetition algorithm that helps you learn more words in less time.",
                image: "/vocab-tool-demo.jpg"
            },
        ],
        primaryButton = "See Them in Action",
        secondaryButton = "All Features"
    } = $props();

    let activeTab = $state(features[0].id);

    function setActiveTab(tabId) {
        activeTab = tabId;
    }
</script>

<SectionWrapper --bg-color="var(--purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="features-section">
    <div class="features-header">
        <P1>{sectionLabel}</P1>
        <H2>{@html headline}</H2>
        <P1>{subheadline}</P1>
    </div>

    <!-- Feature Tabs Navigation -->
    <div class="feature-tabs">
        {#each features as feature}
            <button
                class="feature-tab {activeTab === feature.id ? 'active' : ''}"
                onclick={() => setActiveTab(feature.id)}
                style:border-color={feature.color}
            >
                <div class="tab-icon" style:background-color={feature.color}>
                    {@html feature.icon}
                </div>
                <span class="tab-title">{feature.title}</span>
            </button>
        {/each}
    </div>

    <!-- Feature Content Carousel -->
    <div class="feature-carousel">
        {#each features as feature}
            <div class="feature-content-panel {activeTab === feature.id ? 'active' : ''}" id="tab-content-{feature.id}">
                <div class="feature-info">
                    <div class="feature-info-content">
                        <H2>{feature.title}</H2>
                        <P1>{feature.description}</P1>
                    </div>
                </div>
                <div class="feature-visual">
                    <div class="feature-image-placeholder" style:background-color={feature.color}>
                        <div class="placeholder-text">
                            <P2>{feature.title} Demo</P2>
                            <div class="play-icon">▶</div>
                        </div>
                    </div>
                </div>
            </div>
        {/each}
    </div>
</div>
</SectionWrapper>

<style>
    /* Features Section */
    .features-section {
        max-width: 90rem;
        width: 100%;
        text-align: center;
        position: relative;
    }

    .features-header {
        margin: 0 auto 5rem auto;
        position: relative;
        padding: 2rem;
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        width: fit-content;
    }

    /* Feature Tabs Navigation */
    .feature-tabs {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 4rem;
        flex-wrap: wrap;
    }

    .feature-tab {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-family: inherit;
        font-size: 1rem;
        font-weight: 600;
        color: var(--pitch-black);
        transition: all 0.2s ease;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        position: relative;
    }

    .feature-tab:hover {
        transform: translateY(-0.125rem);
        box-shadow: 0.375rem 0.375rem 0 var(--pitch-black);
    }

    .feature-tab.active {
        background: var(--yellow);
        transform: translateY(-0.25rem);
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        border-width: 0.375rem;
    }

    .tab-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.125rem solid var(--pitch-black);
    }

    .tab-title {
        white-space: nowrap;
    }

    /* Feature Carousel */
    .feature-carousel {
        position: relative;
        overflow: hidden;
        border-radius: 1rem;
        border: 0.25rem solid var(--pitch-black);
        box-shadow: 1rem 1rem 0 var(--pitch-black);
        background: var(--white);
    }

    .feature-content-panel {
        display: none;
        flex-direction: column;
        min-height: 25rem;
    }

    .feature-content-panel.active {
        display: flex;
    }

    .feature-visual {
        padding: 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: 0.25rem solid var(--pitch-black);
    }

    .feature-image-placeholder {
        width: 100%;
        height: 20rem;
        border-radius: 0.75rem;
        border: 0.25rem solid var(--pitch-black);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        position: relative;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .feature-image-placeholder:hover {
        transform: scale(1.02);
    }

    .placeholder-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: var(--pitch-black);
        font-weight: 700;
    }

    .play-icon {
        background: var(--yellow);
        color: var(--pitch-black);
        width: 4rem;
        height: 4rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        border: 0.25rem solid var(--pitch-black);
        transition: transform 0.2s ease;
    }

    .feature-image-placeholder:hover .play-icon {
        transform: scale(1.1);
    }

    .feature-info {
        padding: 2rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .feature-info-content {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        width: 100%;
    }

    .feature-action {
        margin-top: 1rem;
    }

    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .features-header {
            margin-bottom: 3rem;
        }

        .feature-tabs {
            gap: 0.5rem;
            margin-bottom: 3rem;
        }

        .feature-tab {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }

        .tab-icon {
            width: 2rem;
            height: 2rem;
        }

        .tab-title {
            display: none;
        }

        .feature-content-panel {
            min-height: auto;
        }

        .feature-visual {
            padding: 1.5rem;
        }

        .feature-image-placeholder {
            height: 15rem;
        }

        .feature-info {
            padding: 1.5rem;
        }
    }

    @media (max-width: 30rem) {
        .feature-tabs {
            justify-content: flex-start;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        .feature-tab {
            flex-shrink: 0;
        }
    }
</style>
